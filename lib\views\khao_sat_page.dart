import 'package:flutter/material.dart';
import '../controllers/auth_controller.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import 'login_page.dart';

class KhaoSatPage extends StatefulWidget {
  const KhaoSatPage({super.key});

  @override
  State<KhaoSatPage> createState() => _KhaoSatPageState();
}

class _KhaoSatPageState extends State<KhaoSatPage> {
  final AuthController _authController = AuthController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _khaoSatList = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadKhaoSat();
  }

  Future<void> _loadKhaoSat() async {
    try {
      final khaoSatList = await _databaseHelper.getAllKhaoSat();
      setState(() {
        _khaoSatList = khaoSatList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải khảo sát: $e');
    }
  }

  Future<void> _logout() async {
    await _authController.logout();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _authController.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Khảo Sát'),
        backgroundColor: const Color(0xFF4285F4),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Đăng xuất',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Thông tin user
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: Colors.blue[50],
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Xin chào, ${currentUser?.hoTen ?? 'User'}!',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'ID: ${currentUser?.id} - Giới tính: ${currentUser?.gioiTinh}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // Danh sách khảo sát
                Expanded(
                  child: _khaoSatList.isEmpty
                      ? const Center(
                          child: Text(
                            'Không có khảo sát nào',
                            style: TextStyle(fontSize: 16),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _khaoSatList.length,
                          itemBuilder: (context, index) {
                            final khaoSat = _khaoSatList[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              elevation: 2,
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: const Color(0xFF4285F4),
                                  child: Text(
                                    '${khaoSat.id}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  khaoSat.noiDungKhaoSat,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                subtitle: khaoSat.idKsCha != null
                                    ? Text('Khảo sát con của ID: ${khaoSat.idKsCha}')
                                    : const Text('Khảo sát gốc'),
                                trailing: const Icon(Icons.arrow_forward_ios),
                                onTap: () {
                                  // Xử lý khi tap vào khảo sát
                                  _showKhaoSatDetail(khaoSat);
                                },
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }

  void _showKhaoSatDetail(KhaoSat khaoSat) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Khảo sát #${khaoSat.id}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Nội dung:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(khaoSat.noiDungKhaoSat),
              const SizedBox(height: 16),
              if (khaoSat.idKsCha != null) ...[
                Text(
                  'Khảo sát cha: ${khaoSat.idKsCha}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Đóng'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Thực hiện khảo sát
                _performSurvey(khaoSat);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
              ),
              child: const Text('Thực hiện'),
            ),
          ],
        );
      },
    );
  }

  void _performSurvey(KhaoSat khaoSat) {
    // Hiển thị thông báo đơn giản
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã thực hiện khảo sát: ${khaoSat.noiDungKhaoSat}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
