import 'package:flutter/material.dart';
import '../controllers/auth_controller.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import 'login_page.dart';
import 'thong_ke_page.dart';

class KhaoSatPage extends StatefulWidget {
  const KhaoSatPage({super.key});

  @override
  State<KhaoSatPage> createState() => _KhaoSatPageState();
}

class _KhaoSatPageState extends State<KhaoSatPage> {
  final AuthController _authController = AuthController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _parentSurveys = [];
  final Map<int, List<KhaoSat>> _childSurveys = {};
  final Map<int, bool> _expandedSurveys = {};
  final Map<int, bool> _selectedAnswers = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadKhaoSat();
  }

  Future<void> _loadKhaoSat() async {
    try {
      final allSurveys = await _databaseHelper.getAllKhaoSat();
      print('Tổng số khảo sát: ${allSurveys.length}');

      // In ra tất cả khảo sát để debug
      for (var survey in allSurveys) {
        print(
          'Khảo sát ID: ${survey.id}, Nội dung: ${survey.noiDungKhaoSat}, ID cha: ${survey.idKsCha}',
        );
      }

      // Phân loại khảo sát cha và con
      final parentSurveys = allSurveys.where((s) => s.idKsCha == null).toList();
      final childSurveys = <int, List<KhaoSat>>{};

      print('Số khảo sát cha: ${parentSurveys.length}');

      for (var parent in parentSurveys) {
        final children =
            allSurveys.where((s) => s.idKsCha == parent.id).toList();
        print('Khảo sát cha ${parent.id} có ${children.length} câu hỏi con');
        childSurveys[parent.id!] = children;
        _expandedSurveys[parent.id!] = false;

        // Khởi tạo trạng thái checkbox cho các câu hỏi con
        for (var child in children) {
          _selectedAnswers[child.id!] = false;
        }
      }

      setState(() {
        _parentSurveys = parentSurveys;
        _childSurveys.clear();
        _childSurveys.addAll(childSurveys);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải khảo sát: $e');
    }
  }

  Future<void> _logout() async {
    await _authController.logout();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );
    }
  }

  void _toggleExpansion(int surveyId) {
    setState(() {
      _expandedSurveys[surveyId] = !(_expandedSurveys[surveyId] ?? false);
    });
  }

  void _toggleAnswer(int questionId) {
    setState(() {
      _selectedAnswers[questionId] = !(_selectedAnswers[questionId] ?? false);
    });
  }

  void _submitSurvey() {
    // Kiểm tra xem có câu hỏi nào được chọn không
    final selectedQuestions =
        _selectedAnswers.entries
            .where((entry) => entry.value == true)
            .map((entry) => entry.key)
            .toList();

    if (selectedQuestions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ít nhất một câu trả lời!'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Chuyển đến trang thống kê
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ThongKePage(selectedAnswers: _selectedAnswers),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách khảo sát'),
        backgroundColor: Colors.purple[100],
        foregroundColor: Colors.black,
        leading: IconButton(icon: const Icon(Icons.home), onPressed: _logout),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Danh sách khảo sát
                  Expanded(
                    child:
                        _parentSurveys.isEmpty
                            ? const Center(
                              child: Text(
                                'Không có khảo sát nào',
                                style: TextStyle(fontSize: 16),
                              ),
                            )
                            : ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: _parentSurveys.length,
                              itemBuilder: (context, index) {
                                final parentSurvey = _parentSurveys[index];
                                final children =
                                    _childSurveys[parentSurvey.id!] ?? [];
                                final isExpanded =
                                    _expandedSurveys[parentSurvey.id!] ?? false;

                                return Card(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: Column(
                                    children: [
                                      // Header của khảo sát cha
                                      ListTile(
                                        title: Text(
                                          parentSurvey.noiDungKhaoSat,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        trailing: Icon(
                                          isExpanded
                                              ? Icons.keyboard_arrow_up
                                              : Icons.keyboard_arrow_down,
                                        ),
                                        onTap:
                                            () => _toggleExpansion(
                                              parentSurvey.id!,
                                            ),
                                      ),

                                      // Danh sách câu hỏi con
                                      if (isExpanded)
                                        ...children.map((child) {
                                          final isSelected =
                                              _selectedAnswers[child.id!] ??
                                              false;
                                          return ListTile(
                                            leading: Checkbox(
                                              value: isSelected,
                                              onChanged:
                                                  (value) =>
                                                      _toggleAnswer(child.id!),
                                              activeColor: Colors.blue,
                                            ),
                                            title: Text(
                                              child.noiDungKhaoSat,
                                              style: const TextStyle(
                                                fontSize: 14,
                                              ),
                                            ),
                                            onTap:
                                                () => _toggleAnswer(child.id!),
                                          );
                                        }),
                                    ],
                                  ),
                                );
                              },
                            ),
                  ),

                  // Nút bình chọn
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _submitSurvey,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Bình chọn',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }
}
