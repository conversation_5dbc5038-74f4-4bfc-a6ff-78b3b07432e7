import 'package:flutter/material.dart';
import '../controllers/auth_controller.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import '../models/user.dart';
import 'login_page.dart';
import 'admin_survey_management_page.dart';
import 'admin_statistics_page.dart';

class AdminPage extends StatefulWidget {
  const AdminPage({super.key});

  @override
  State<AdminPage> createState() => _AdminPageState();
}

class _AdminPageState extends State<AdminPage> {
  final AuthController _authController = AuthController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _surveys = [];
  List<User> _users = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final surveys = await _databaseHelper.getAllKhaoSat();
      final users = await _databaseHelper.getAllUsers();
      
      setState(() {
        _surveys = surveys;
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải dữ liệu admin: $e');
    }
  }

  Future<void> _logout() async {
    await _authController.logout();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _authController.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quản trị hệ thống'),
        backgroundColor: Colors.red[100],
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Đăng xuất',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Thông tin admin
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Icon(
                              Icons.admin_panel_settings,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Xin chào, ${currentUser?.hoTen ?? 'Admin'}!',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'ID: ${currentUser?.id} - Role: ${currentUser?.role}',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Thống kê tổng quan
                  const Text(
                    'Thống kê tổng quan',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Tổng khảo sát',
                          '${_surveys.length}',
                          Icons.poll,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildStatCard(
                          'Người dùng',
                          '${_users.length}',
                          Icons.people,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Khảo sát cha',
                          '${_surveys.where((s) => s.idKsCha == null).length}',
                          Icons.folder,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildStatCard(
                          'Câu hỏi',
                          '${_surveys.where((s) => s.idKsCha != null).length}',
                          Icons.quiz,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Menu quản lý
                  const Text(
                    'Quản lý hệ thống',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildMenuCard(
                    'Quản lý khảo sát',
                    'Thêm, sửa, xóa khảo sát và câu hỏi',
                    Icons.edit_note,
                    Colors.blue,
                    () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminSurveyManagementPage(),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  _buildMenuCard(
                    'Xem thống kê chi tiết',
                    'Xem báo cáo và thống kê chi tiết',
                    Icons.analytics,
                    Colors.green,
                    () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminStatisticsPage(),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  _buildMenuCard(
                    'Quản lý người dùng',
                    'Xem danh sách người dùng hệ thống',
                    Icons.people_alt,
                    Colors.orange,
                    () {
                      _showUsersDialog();
                    },
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _showUsersDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Danh sách người dùng'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: _users.length,
              itemBuilder: (context, index) {
                final user = _users[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: user.isAdmin ? Colors.red : Colors.blue,
                    child: Text('${user.id}'),
                  ),
                  title: Text(user.hoTen),
                  subtitle: Text('${user.gioiTinh} - ${user.role}'),
                  trailing: user.isAdmin 
                      ? const Icon(Icons.admin_panel_settings, color: Colors.red)
                      : const Icon(Icons.person, color: Colors.blue),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Đóng'),
            ),
          ],
        );
      },
    );
  }
}
