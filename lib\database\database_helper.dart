import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'khao_sat.db');
    return await openDatabase(
      path,
      version: 2, // Tăng version để force recreate
      onCreate: _onCreate,
      onUpgrade: (db, oldVersion, newVersion) async {
        // Drop all tables and recreate
        await db.execute('DROP TABLE IF EXISTS CTThucHienKS');
        await db.execute('DROP TABLE IF EXISTS ThucHienKS');
        await db.execute('DROP TABLE IF EXISTS KhaoSat');
        await db.execute('DROP TABLE IF EXISTS User');
        await _onCreate(db, newVersion);
      },
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Tạo bảng User
    await db.execute('''
      CREATE TABLE User (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        matKhau TEXT NOT NULL,
        hoTen TEXT NOT NULL,
        gioiTinh TEXT NOT NULL,
        hinhMinhHoa TEXT
      )
    ''');

    // Tạo bảng KhaoSat
    await db.execute('''
      CREATE TABLE KhaoSat (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        noiDungKhaoSat TEXT NOT NULL,
        idKsCha INTEGER,
        FOREIGN KEY (idKsCha) REFERENCES KhaoSat (id)
      )
    ''');

    // Tạo bảng ThucHienKS
    await db.execute('''
      CREATE TABLE ThucHienKS (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ngay INTEGER NOT NULL,
        idKhaoSat INTEGER NOT NULL,
        idUser INTEGER NOT NULL,
        FOREIGN KEY (idKhaoSat) REFERENCES KhaoSat (id),
        FOREIGN KEY (idUser) REFERENCES User (id)
      )
    ''');

    // Tạo bảng CTThucHienKS
    await db.execute('''
      CREATE TABLE CTThucHienKS (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        idKs INTEGER NOT NULL,
        binhChon TEXT NOT NULL,
        FOREIGN KEY (idKs) REFERENCES KhaoSat (id)
      )
    ''');

    // Thêm dữ liệu mẫu
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    print('Đang thêm dữ liệu mẫu...');
    // Thêm user mẫu (mật khẩu: 123456)
    await db.insert('User', {
      'id': 1,
      'matKhau': '123456',
      'hoTen': 'Lê Đức Trung',
      'gioiTinh': 'Nam',
      'hinhMinhHoa': null,
    });

    await db.insert('User', {
      'id': 2,
      'matKhau': 'admin',
      'hoTen': 'Admin User',
      'gioiTinh': 'Nữ',
      'hinhMinhHoa': null,
    });

    // Thêm khảo sát cha
    await db.insert('KhaoSat', {
      'id': 1,
      'noiDungKhaoSat': 'Khảo sát về sức khỏe cá nhân',
      'idKsCha': null,
    });

    await db.insert('KhaoSat', {
      'id': 2,
      'noiDungKhaoSat': 'Khảo sát về chế độ dinh dưỡng',
      'idKsCha': null,
    });

    // Thêm khảo sát con cho "Khảo sát về sức khỏe cá nhân"
    await db.insert('KhaoSat', {
      'id': 3,
      'noiDungKhaoSat': 'Bạn có thể cảm thấy mình khỏe mạnh?',
      'idKsCha': 1,
    });

    await db.insert('KhaoSat', {
      'id': 4,
      'noiDungKhaoSat': 'Bạn có ngủ đủ 7-8 tiếng mỗi ngày không?',
      'idKsCha': 1,
    });

    await db.insert('KhaoSat', {
      'id': 5,
      'noiDungKhaoSat': 'Bạn có tập thể dục khỏe định kỳ không?',
      'idKsCha': 1,
    });

    // Thêm khảo sát con cho "Khảo sát về chế độ dinh dưỡng"
    await db.insert('KhaoSat', {
      'id': 6,
      'noiDungKhaoSat': 'Bạn có ăn đủ rau và trái cây mỗi ngày không?',
      'idKsCha': 2,
    });

    await db.insert('KhaoSat', {
      'id': 7,
      'noiDungKhaoSat': 'Bạn có thường xuyên ăn thức ăn nhanh không?',
      'idKsCha': 2,
    });

    await db.insert('KhaoSat', {
      'id': 8,
      'noiDungKhaoSat': 'Bạn có uống đủ nước mỗi ngày không?',
      'idKsCha': 2,
    });

    print('Đã thêm xong dữ liệu mẫu!');
  }

  // CRUD operations cho User
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('User', user.toMap());
  }

  Future<User?> getUserById(int id) async {
    final db = await database;
    final maps = await db.query('User', where: 'id = ?', whereArgs: [id]);
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> authenticateUser(int id, String password) async {
    final db = await database;
    final maps = await db.query(
      'User',
      where: 'id = ? AND matKhau = ?',
      whereArgs: [id, password],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final maps = await db.query('User');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  // CRUD operations cho KhaoSat
  Future<int> insertKhaoSat(KhaoSat khaoSat) async {
    final db = await database;
    return await db.insert('KhaoSat', khaoSat.toMap());
  }

  Future<List<KhaoSat>> getAllKhaoSat() async {
    final db = await database;
    final maps = await db.query('KhaoSat');
    return List.generate(maps.length, (i) => KhaoSat.fromMap(maps[i]));
  }

  // CRUD operations cho ThucHienKS
  Future<int> insertThucHienKS(ThucHienKS thucHienKS) async {
    final db = await database;
    return await db.insert('ThucHienKS', thucHienKS.toMap());
  }

  // CRUD operations cho CTThucHienKS
  Future<int> insertCTThucHienKS(CTThucHienKS ctThucHienKS) async {
    final db = await database;
    return await db.insert('CTThucHienKS', ctThucHienKS.toMap());
  }
}
