import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';

class AdminSurveyManagementPage extends StatefulWidget {
  const AdminSurveyManagementPage({super.key});

  @override
  State<AdminSurveyManagementPage> createState() => _AdminSurveyManagementPageState();
}

class _AdminSurveyManagementPageState extends State<AdminSurveyManagementPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _surveys = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSurveys();
  }

  Future<void> _loadSurveys() async {
    try {
      final surveys = await _databaseHelper.getAllKhaoSat();
      setState(() {
        _surveys = surveys;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải khảo sát: $e');
    }
  }

  Future<void> _addSurvey(String content, int? parentId) async {
    try {
      final survey = KhaoSat(
        noiDungKhaoSat: content,
        idKsCha: parentId,
      );
      
      await _databaseHelper.insertKhaoSat(survey);
      await _loadSurveys();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã thêm khảo sát thành công!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi thêm khảo sát: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddSurveyDialog({KhaoSat? parentSurvey}) {
    final TextEditingController controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(parentSurvey == null ? 'Thêm khảo sát mới' : 'Thêm câu hỏi con'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (parentSurvey != null) ...[
                Text(
                  'Khảo sát cha: ${parentSurvey.noiDungKhaoSat}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),
              ],
              TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: parentSurvey == null ? 'Nội dung khảo sát' : 'Nội dung câu hỏi',
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  _addSurvey(controller.text.trim(), parentSurvey?.id);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Thêm'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final parentSurveys = _surveys.where((s) => s.idKsCha == null).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quản lý khảo sát'),
        backgroundColor: Colors.blue[100],
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddSurveyDialog(),
            tooltip: 'Thêm khảo sát mới',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadSurveys,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: parentSurveys.length,
                itemBuilder: (context, index) {
                  final parentSurvey = parentSurveys[index];
                  final childSurveys = _surveys
                      .where((s) => s.idKsCha == parentSurvey.id)
                      .toList();

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: ExpansionTile(
                      title: Text(
                        parentSurvey.noiDungKhaoSat,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Text('${childSurveys.length} câu hỏi con'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.add_circle_outline),
                            onPressed: () => _showAddSurveyDialog(parentSurvey: parentSurvey),
                            tooltip: 'Thêm câu hỏi con',
                          ),
                          const Icon(Icons.expand_more),
                        ],
                      ),
                      children: [
                        if (childSurveys.isEmpty)
                          const Padding(
                            padding: EdgeInsets.all(16),
                            child: Text(
                              'Chưa có câu hỏi con nào',
                              style: TextStyle(
                                color: Colors.grey,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          )
                        else
                          ...childSurveys.map((child) {
                            return ListTile(
                              leading: const Icon(
                                Icons.subdirectory_arrow_right,
                                color: Colors.grey,
                              ),
                              title: Text(child.noiDungKhaoSat),
                              subtitle: Text('ID: ${child.id}'),
                              trailing: PopupMenuButton(
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(Icons.edit),
                                        SizedBox(width: 8),
                                        Text('Sửa'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text('Xóa', style: TextStyle(color: Colors.red)),
                                      ],
                                    ),
                                  ),
                                ],
                                onSelected: (value) {
                                  if (value == 'edit') {
                                    _showEditSurveyDialog(child);
                                  } else if (value == 'delete') {
                                    _showDeleteConfirmDialog(child);
                                  }
                                },
                              ),
                            );
                          }).toList(),
                      ],
                    ),
                  );
                },
              ),
            ),
    );
  }

  void _showEditSurveyDialog(KhaoSat survey) {
    final TextEditingController controller = TextEditingController(
      text: survey.noiDungKhaoSat,
    );
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sửa nội dung'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'Nội dung',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement update functionality
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Chức năng sửa sẽ được cập nhật sau!'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              child: const Text('Lưu'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmDialog(KhaoSat survey) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Xác nhận xóa'),
          content: Text('Bạn có chắc muốn xóa: "${survey.noiDungKhaoSat}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement delete functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Chức năng xóa sẽ được cập nhật sau!'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Xóa'),
            ),
          ],
        );
      },
    );
  }
}
