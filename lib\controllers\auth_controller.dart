import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../models/user.dart';

class AuthController {
  static final AuthController _instance = AuthController._internal();
  factory AuthController() => _instance;
  AuthController._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  User? _currentUser;

  User? get currentUser => _currentUser;

  // Đăng nhập
  Future<bool> login(int userId, String password, bool rememberMe) async {
    try {
      final user = await _databaseHelper.authenticateUser(userId, password);
      if (user != null) {
        _currentUser = user;
        
        if (rememberMe) {
          await _saveLoginInfo(userId, password);
        } else {
          await _clearLoginInfo();
        }
        
        return true;
      }
      return false;
    } catch (e) {
      print('Lỗi đăng nhập: $e');
      return false;
    }
  }

  // <PERSON><PERSON><PERSON> thông tin đăng nhập
  Future<void> _saveLoginInfo(int userId, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('saved_user_id', userId);
    await prefs.setString('saved_password', password);
    await prefs.setBool('remember_login', true);
  }

  // Xóa thông tin đăng nhập đã lưu
  Future<void> _clearLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('saved_user_id');
    await prefs.remove('saved_password');
    await prefs.setBool('remember_login', false);
  }

  // Lấy thông tin đăng nhập đã lưu
  Future<Map<String, dynamic>?> getSavedLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberLogin = prefs.getBool('remember_login') ?? false;
    
    if (rememberLogin) {
      final userId = prefs.getInt('saved_user_id');
      final password = prefs.getString('saved_password');
      
      if (userId != null && password != null) {
        return {
          'userId': userId,
          'password': password,
          'rememberMe': true,
        };
      }
    }
    
    return null;
  }

  // Kiểm tra xem có thông tin đăng nhập đã lưu không
  Future<bool> hasRememberedLogin() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('remember_login') ?? false;
  }

  // Đăng xuất
  Future<void> logout() async {
    _currentUser = null;
    await _clearLoginInfo();
  }

  // Tự động đăng nhập nếu có thông tin đã lưu
  Future<bool> autoLogin() async {
    final savedInfo = await getSavedLoginInfo();
    if (savedInfo != null) {
      return await login(
        savedInfo['userId'],
        savedInfo['password'],
        savedInfo['rememberMe'],
      );
    }
    return false;
  }
}
