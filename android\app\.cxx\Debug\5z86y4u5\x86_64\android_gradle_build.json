{"buildFiles": ["D:\\File\\FileFlutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ktra\\c_33_leductrung_2001225676\\android\\app\\.cxx\\Debug\\5z86y4u5\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\File\\FileAndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ktra\\c_33_leductrung_2001225676\\android\\app\\.cxx\\Debug\\5z86y4u5\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\File\\FileAndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\File\\FileAndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}