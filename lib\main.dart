import 'package:flutter/material.dart';
import 'views/login_page.dart';
import 'controllers/auth_controller.dart';
import 'views/khao_sat_page.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ứng dụng khảo sát',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF4285F4)),
        useMaterial3: true,
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final AuthController _authController = AuthController();

  @override
  void initState() {
    super.initState();
    _checkAutoLogin();
  }

  Future<void> _checkAutoLogin() async {
    // Đợi một chút để hiển thị splash screen
    await Future.delayed(const Duration(seconds: 1));

    // Kiểm tra xem có thông tin đăng nhập đã lưu không
    final hasAutoLogin = await _authController.autoLogin();

    if (mounted) {
      if (hasAutoLogin) {
        // Tự động đăng nhập thành công, chuyển đến trang khảo sát
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const KhaoSatPage()),
        );
      } else {
        // Chuyển đến trang đăng nhập
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginPage()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF4285F4),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.poll, size: 80, color: Colors.white),
            SizedBox(height: 24),
            Text(
              'Ứng dụng Khảo sát',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 16),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
