                        -HD:\File\FileFlutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\File\FileAndroidSDK\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\File\FileAndroidSDK\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\File\FileAndroidSDK\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\File\FileAndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Ktra\c_33_leductrung_2001225676\build\app\intermediates\cxx\Debug\5z86y4u5\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Ktra\c_33_leductrung_2001225676\build\app\intermediates\cxx\Debug\5z86y4u5\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\Ktra\c_33_leductrung_2001225676\android\app\.cxx\Debug\5z86y4u5\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2