class User {
  final int? id;
  final String matKhau;
  final String hoTen;
  final String gioiTinh;
  final String? hinhMinhHoa;
  final String role;

  User({
    this.id,
    required this.matKhau,
    required this.hoTen,
    required this.gioiTinh,
    this.hinhMinhHoa,
    this.role = 'user',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'matKhau': matKhau,
      'hoTen': hoTen,
      'gioiTinh': gioiTinh,
      'hinhMinhHoa': hinhMinhHoa,
      'role': role,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      matKhau: map['matKhau'],
      hoTen: map['hoTen'],
      gioiTinh: map['gioiTinh'],
      hinhMinhHoa: map['hinhMinhHoa'],
      role: map['role'] ?? 'user',
    );
  }

  User copyWith({
    int? id,
    String? matKhau,
    String? hoTen,
    String? gioiTinh,
    String? hinhMinhHoa,
    String? role,
  }) {
    return User(
      id: id ?? this.id,
      matKhau: matKhau ?? this.matKhau,
      hoTen: hoTen ?? this.hoTen,
      gioiTinh: gioiTinh ?? this.gioiTinh,
      hinhMinhHoa: hinhMinhHoa ?? this.hinhMinhHoa,
      role: role ?? this.role,
    );
  }

  bool get isAdmin => role == 'admin';
}
