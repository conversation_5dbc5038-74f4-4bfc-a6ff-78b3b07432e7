import 'package:flutter/material.dart';
import 'login_page.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';

class ThongKePage extends StatefulWidget {
  final Map<int, bool> selectedAnswers;

  const ThongKePage({super.key, required this.selectedAnswers});

  @override
  State<ThongKePage> createState() => _ThongKePageState();
}

class _ThongKePageState extends State<ThongKePage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  Map<int, int> _statistics = {};
  List<KhaoSat> _allSurveys = [];
  int _totalParticipants = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    try {
      final statistics = await _databaseHelper.getSurveyStatistics();
      final surveys = await _databaseHelper.getAllKhaoSat();
      final totalParticipants = await _databaseHelper.getTotalParticipants();

      setState(() {
        _statistics = statistics;
        _allSurveys = surveys;
        _totalParticipants = totalParticipants;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải thống kê: $e');
    }
  }

  void _goToLogin() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Thống kê kết quả'),
          backgroundColor: Colors.purple[100],
          foregroundColor: Colors.black,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // Lấy danh sách khảo sát cha và con
    final parentSurveys = _allSurveys.where((s) => s.idKsCha == null).toList();
    final childSurveys = _allSurveys.where((s) => s.idKsCha != null).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê kết quả'),
        backgroundColor: Colors.purple[100],
        foregroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: _goToLogin,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tiêu đề và thông tin tổng quan
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Thống kê tổng hợp',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildInfoCard(
                          'Tổng người tham gia',
                          '$_totalParticipants',
                          Colors.blue,
                        ),
                        _buildInfoCard(
                          'Tổng câu hỏi',
                          '${childSurveys.length}',
                          Colors.green,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Thống kê chi tiết theo khảo sát
            const Text(
              'Chi tiết theo khảo sát',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            Expanded(
              child: ListView.builder(
                itemCount: parentSurveys.length,
                itemBuilder: (context, index) {
                  final parent = parentSurveys[index];
                  final children =
                      _allSurveys.where((s) => s.idKsCha == parent.id).toList();

                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ExpansionTile(
                      title: Text(
                        parent.noiDungKhaoSat,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      children:
                          children.map((child) {
                            final voteCount = _statistics[child.id] ?? 0;
                            final isUserSelected =
                                widget.selectedAnswers[child.id] == true;

                            return ListTile(
                              leading: Icon(
                                isUserSelected
                                    ? Icons.check_circle
                                    : Icons.radio_button_unchecked,
                                color:
                                    isUserSelected ? Colors.green : Colors.grey,
                              ),
                              title: Text(child.noiDungKhaoSat),
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '($voteCount)',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Nút về trang chủ
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: _goToLogin,
                icon: const Icon(Icons.home),
                label: const Text('Về trang đăng nhập'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
