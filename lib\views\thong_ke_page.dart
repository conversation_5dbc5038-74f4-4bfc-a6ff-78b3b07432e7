import 'package:flutter/material.dart';
import 'login_page.dart';

class ThongKePage extends StatefulWidget {
  final Map<int, bool> selectedAnswers;

  const ThongKePage({super.key, required this.selectedAnswers});

  @override
  State<ThongKePage> createState() => _ThongKePageState();
}

class _ThongKePageState extends State<ThongKePage> {
  late Map<String, int> _statistics;

  @override
  void initState() {
    super.initState();
    _calculateStatistics();
  }

  void _calculateStatistics() {
    // Tính toán thống kê dựa trên câu trả lời đã chọn
    final selectedCount = widget.selectedAnswers.values.where((v) => v == true).length;
    final totalQuestions = widget.selectedAnswers.length;
    final unselectedCount = totalQuestions - selectedCount;

    _statistics = {
      'Đã chọn': selectedCount,
      'Ch<PERSON>a chọn': unselectedCount,
      'Tổng cộng': totalQuestions,
    };
  }

  void _goToLogin() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê kết quả'),
        backgroundColor: Colors.purple[100],
        foregroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: _goToLogin,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tiêu đề
            const Text(
              'Kết quả bình chọn',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Biểu đồ cột đơn giản
            Expanded(
              child: Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Text(
                        'Thống kê câu trả lời',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Biểu đồ cột
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            _buildBarChart(
                              'Đã chọn',
                              _statistics['Đã chọn']!,
                              Colors.green,
                              _statistics['Tổng cộng']!,
                            ),
                            _buildBarChart(
                              'Chưa chọn',
                              _statistics['Chưa chọn']!,
                              Colors.red,
                              _statistics['Tổng cộng']!,
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Thống kê số liệu
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            _buildStatRow('Câu hỏi đã chọn:', '${_statistics['Đã chọn']}'),
                            const SizedBox(height: 8),
                            _buildStatRow('Câu hỏi chưa chọn:', '${_statistics['Chưa chọn']}'),
                            const SizedBox(height: 8),
                            _buildStatRow('Tổng số câu hỏi:', '${_statistics['Tổng cộng']}'),
                            const SizedBox(height: 8),
                            _buildStatRow(
                              'Tỷ lệ hoàn thành:',
                              '${((_statistics['Đã chọn']! / _statistics['Tổng cộng']!) * 100).toStringAsFixed(1)}%',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Nút về trang chủ
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: _goToLogin,
                icon: const Icon(Icons.home),
                label: const Text('Về trang đăng nhập'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBarChart(String label, int value, Color color, int maxValue) {
    final height = maxValue > 0 ? (value / maxValue) * 200.0 : 0.0;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '$value',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 60,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }
}
