class KhaoSat {
  final int? id;
  final String noiDungKhaoSat;
  final int? idKsCha;

  KhaoSat({
    this.id,
    required this.noiDungKhaoSat,
    this.idKsCha,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'noiDungKhaoSat': noiDungKhaoSat,
      'idKsCha': idKsCha,
    };
  }

  factory KhaoSat.fromMap(Map<String, dynamic> map) {
    return KhaoSat(
      id: map['id'],
      noiDungKhaoSat: map['noiDungKhaoSat'],
      idKsCha: map['idKsCha'],
    );
  }

  KhaoSat copyWith({
    int? id,
    String? noiDungKhaoSat,
    int? idKsCha,
  }) {
    return KhaoSat(
      id: id ?? this.id,
      noiDungKhaoSat: noiDungKhaoSat ?? this.noiDungKhaoSat,
      idKsCha: idKsCha ?? this.idKsCha,
    );
  }
}
