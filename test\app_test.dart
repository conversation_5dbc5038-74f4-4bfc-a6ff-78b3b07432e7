import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:c_33_leductrung_2001225676/main.dart';

void main() {
  group('Ứng dụng Khảo sát Tests', () {
    testWidgets('Ki<PERSON>m tra splash screen hiển thị', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Verify that splash screen is displayed
      expect(find.text('Ứng dụng Khảo sát'), findsOneWidget);
      expect(find.byIcon(Icons.poll), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Kiểm tra chuyển đến trang đăng nhập', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Wait for splash screen to finish
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify that login page is displayed
      expect(find.text('Đ<PERSON><PERSON> Nhập'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Ghi nhớ mật khẩu'), findsOneWidget);
      expect(find.text('ĐĂNG NHẬP'), findsOneWidget);
    });

    testWidgets('Kiểm tra validation form đăng nhập', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Tap login button without entering data
      await tester.tap(find.text('ĐĂNG NHẬP'));
      await tester.pump();

      // Verify validation messages appear
      expect(find.text('Vui lòng nhập username'), findsOneWidget);
      expect(find.text('Vui lòng nhập mật khẩu'), findsOneWidget);
    });

    testWidgets('Kiểm tra toggle hiện/ẩn mật khẩu', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify visibility toggle icon exists
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);

      // Tap the visibility toggle
      await tester.tap(find.byIcon(Icons.visibility_off));
      await tester.pump();

      // Verify icon changed to visible
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('Kiểm tra checkbox ghi nhớ mật khẩu', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Find checkbox
      final checkbox = find.byType(Checkbox);

      // Initially checkbox should be unchecked
      Checkbox checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, isFalse);

      // Tap checkbox
      await tester.tap(checkbox);
      await tester.pump();

      // Checkbox should now be checked
      checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, isTrue);
    });
  });
}
