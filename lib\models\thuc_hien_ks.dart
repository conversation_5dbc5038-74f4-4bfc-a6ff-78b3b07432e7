class ThucHienKS {
  final int? id;
  final DateTime ngay;
  final int idKhaoSat;
  final int idUser;

  ThucHienKS({
    this.id,
    required this.ngay,
    required this.idKhaoSat,
    required this.idUser,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'ngay': ngay.millisecondsSinceEpoch,
      'idKhaoSat': idKhaoSat,
      'idUser': idUser,
    };
  }

  factory ThucHienKS.fromMap(Map<String, dynamic> map) {
    return ThucHienKS(
      id: map['id'],
      ngay: DateTime.fromMillisecondsSinceEpoch(map['ngay']),
      idKhaoSat: map['idKhaoSat'],
      idUser: map['idUser'],
    );
  }

  ThucHienKS copyWith({
    int? id,
    DateTime? ngay,
    int? idKhaoSat,
    int? idUser,
  }) {
    return ThucHienKS(
      id: id ?? this.id,
      ngay: ngay ?? this.ngay,
      idKhaoSat: idKhaoSat ?? this.idKhaoSat,
      idUser: idUser ?? this.idUser,
    );
  }
}
