import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/user.dart';

class AdminStatisticsPage extends StatefulWidget {
  const AdminStatisticsPage({super.key});

  @override
  State<AdminStatisticsPage> createState() => _AdminStatisticsPageState();
}

class _AdminStatisticsPageState extends State<AdminStatisticsPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _surveys = [];
  List<User> _users = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final surveys = await _databaseHelper.getAllKhaoSat();
      final users = await _databaseHelper.getAllUsers();
      
      setState(() {
        _surveys = surveys;
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải dữ liệu thống kê: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final parentSurveys = _surveys.where((s) => s.idKsCha == null).toList();
    final childSurveys = _surveys.where((s) => s.idKsCha != null).toList();
    final regularUsers = _users.where((u) => !u.isAdmin).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê chi tiết'),
        backgroundColor: Colors.green[100],
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Thống kê tổng quan
                  const Text(
                    'Thống kê tổng quan hệ thống',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Cards thống kê
                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildStatCard(
                        'Tổng khảo sát',
                        '${parentSurveys.length}',
                        Icons.poll,
                        Colors.blue,
                      ),
                      _buildStatCard(
                        'Tổng câu hỏi',
                        '${childSurveys.length}',
                        Icons.quiz,
                        Colors.green,
                      ),
                      _buildStatCard(
                        'Người dùng',
                        '${regularUsers.length}',
                        Icons.people,
                        Colors.orange,
                      ),
                      _buildStatCard(
                        'Quản trị viên',
                        '${_users.where((u) => u.isAdmin).length}',
                        Icons.admin_panel_settings,
                        Colors.red,
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Chi tiết khảo sát
                  const Text(
                    'Chi tiết khảo sát',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  ...parentSurveys.map((parent) {
                    final children = _surveys.where((s) => s.idKsCha == parent.id).toList();
                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              parent.noiDungKhaoSat,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'ID: ${parent.id}',
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            // Biểu đồ đơn giản cho số câu hỏi
                            Row(
                              children: [
                                const Icon(Icons.quiz, size: 16, color: Colors.blue),
                                const SizedBox(width: 8),
                                Text('${children.length} câu hỏi'),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: children.length / 10, // Giả sử max 10 câu hỏi
                                    backgroundColor: Colors.grey[300],
                                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 12),
                            
                            // Danh sách câu hỏi con
                            if (children.isNotEmpty) ...[
                              const Text(
                                'Câu hỏi:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 8),
                              ...children.map((child) {
                                return Padding(
                                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.subdirectory_arrow_right,
                                        size: 16,
                                        color: Colors.grey,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          child.noiDungKhaoSat,
                                          style: const TextStyle(fontSize: 13),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ],
                          ],
                        ),
                      ),
                    );
                  }).toList(),

                  const SizedBox(height: 32),

                  // Thống kê người dùng
                  const Text(
                    'Thống kê người dùng',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Biểu đồ phân bố giới tính
                          Row(
                            children: [
                              Expanded(
                                child: _buildGenderChart(),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Danh sách người dùng
                          const Text(
                            'Danh sách người dùng',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 12),
                          
                          ...regularUsers.take(5).map((user) {
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: user.gioiTinh == 'Nam' ? Colors.blue : Colors.pink,
                                child: Text('${user.id}'),
                              ),
                              title: Text(user.hoTen),
                              subtitle: Text(user.gioiTinh),
                              trailing: Icon(
                                user.gioiTinh == 'Nam' ? Icons.male : Icons.female,
                                color: user.gioiTinh == 'Nam' ? Colors.blue : Colors.pink,
                              ),
                            );
                          }).toList(),
                          
                          if (regularUsers.length > 5)
                            TextButton(
                              onPressed: () {
                                _showAllUsersDialog();
                              },
                              child: Text('Xem tất cả ${regularUsers.length} người dùng'),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderChart() {
    final maleCount = _users.where((u) => u.gioiTinh == 'Nam').length;
    final femaleCount = _users.where((u) => u.gioiTinh == 'Nữ').length;
    final total = maleCount + femaleCount;

    return Column(
      children: [
        const Text(
          'Phân bố giới tính',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              flex: maleCount,
              child: Container(
                height: 20,
                color: Colors.blue,
                child: Center(
                  child: Text(
                    'Nam: $maleCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              flex: femaleCount,
              child: Container(
                height: 20,
                color: Colors.pink,
                child: Center(
                  child: Text(
                    'Nữ: $femaleCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Text(
              'Nam: ${total > 0 ? (maleCount / total * 100).toStringAsFixed(1) : 0}%',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              'Nữ: ${total > 0 ? (femaleCount / total * 100).toStringAsFixed(1) : 0}%',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  void _showAllUsersDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Tất cả người dùng'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              itemCount: _users.length,
              itemBuilder: (context, index) {
                final user = _users[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: user.isAdmin 
                        ? Colors.red 
                        : (user.gioiTinh == 'Nam' ? Colors.blue : Colors.pink),
                    child: Text('${user.id}'),
                  ),
                  title: Text(user.hoTen),
                  subtitle: Text('${user.gioiTinh} - ${user.role}'),
                  trailing: user.isAdmin 
                      ? const Icon(Icons.admin_panel_settings, color: Colors.red)
                      : Icon(
                          user.gioiTinh == 'Nam' ? Icons.male : Icons.female,
                          color: user.gioiTinh == 'Nam' ? Colors.blue : Colors.pink,
                        ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Đóng'),
            ),
          ],
        );
      },
    );
  }
}
