import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class KhaoSatController {
  static final KhaoSatController _instance = KhaoSatController._internal();
  factory KhaoSatController() => _instance;
  KhaoSatController._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Lấy tất cả khảo sát
  Future<List<KhaoSat>> getAllKhaoSat() async {
    try {
      return await _databaseHelper.getAllKhaoSat();
    } catch (e) {
      print('Lỗi lấy danh sách khảo sát: $e');
      return [];
    }
  }

  // Lấy khảo sát theo ID
  Future<KhaoSat?> getKhaoSatById(int id) async {
    try {
      final allKhaoSat = await _databaseHelper.getAllKhaoSat();
      return allKhaoSat.firstWhere((ks) => ks.id == id);
    } catch (e) {
      print('Lỗi lấy khảo sát theo ID: $e');
      return null;
    }
  }

  // Lấy khảo sát con theo ID cha
  Future<List<KhaoSat>> getKhaoSatByParentId(int parentId) async {
    try {
      final allKhaoSat = await _databaseHelper.getAllKhaoSat();
      return allKhaoSat.where((ks) => ks.idKsCha == parentId).toList();
    } catch (e) {
      print('Lỗi lấy khảo sát con: $e');
      return [];
    }
  }

  // Lấy khảo sát gốc (không có cha)
  Future<List<KhaoSat>> getRootKhaoSat() async {
    try {
      final allKhaoSat = await _databaseHelper.getAllKhaoSat();
      return allKhaoSat.where((ks) => ks.idKsCha == null).toList();
    } catch (e) {
      print('Lỗi lấy khảo sát gốc: $e');
      return [];
    }
  }

  // Thêm khảo sát mới
  Future<bool> addKhaoSat(String noiDung, {int? idKsCha}) async {
    try {
      final khaoSat = KhaoSat(
        noiDungKhaoSat: noiDung,
        idKsCha: idKsCha,
      );
      
      final id = await _databaseHelper.insertKhaoSat(khaoSat);
      return id > 0;
    } catch (e) {
      print('Lỗi thêm khảo sát: $e');
      return false;
    }
  }

  // Thực hiện khảo sát
  Future<bool> performSurvey(int khaoSatId, int userId, String binhChon) async {
    try {
      // Tạo bản ghi ThucHienKS
      final thucHienKS = ThucHienKS(
        ngay: DateTime.now(),
        idKhaoSat: khaoSatId,
        idUser: userId,
      );
      
      final thucHienId = await _databaseHelper.insertThucHienKS(thucHienKS);
      
      if (thucHienId > 0) {
        // Tạo bản ghi CTThucHienKS
        final ctThucHienKS = CTThucHienKS(
          idKs: khaoSatId,
          binhChon: binhChon,
        );
        
        final ctId = await _databaseHelper.insertCTThucHienKS(ctThucHienKS);
        return ctId > 0;
      }
      
      return false;
    } catch (e) {
      print('Lỗi thực hiện khảo sát: $e');
      return false;
    }
  }

  // Kiểm tra xem user đã thực hiện khảo sát này chưa
  Future<bool> hasUserPerformedSurvey(int khaoSatId, int userId) async {
    try {
      // Đây là một implementation đơn giản
      // Trong thực tế, bạn có thể cần query database để kiểm tra
      return false; // Giả sử chưa thực hiện
    } catch (e) {
      print('Lỗi kiểm tra khảo sát đã thực hiện: $e');
      return false;
    }
  }

  // Lấy thống kê khảo sát
  Future<Map<String, dynamic>> getSurveyStats(int khaoSatId) async {
    try {
      // Implementation đơn giản cho thống kê
      return {
        'totalResponses': 0,
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      print('Lỗi lấy thống kê khảo sát: $e');
      return {};
    }
  }
}
